/* ===== Responsive Design ===== */

/* Extra Large Devices (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3.5rem;
    }
}

/* Large Devices (1200px and up) */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-content {
        gap: var(--spacing-2xl);
    }
}

/* Medium Devices (992px and up) */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .nav-list {
        gap: var(--spacing-lg);
    }
}

/* Small Devices (768px and up) */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 720px;
        padding: 0 var(--spacing-md);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }

    .navbar {
        padding: var(--spacing-lg) 0;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
    
    .tab-buttons {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .tab-btn {
        flex: 1;
        min-width: 150px;
    }
}

/* Mobile Devices (Below 576px) */
@media (max-width: 575px) {
    .container {
        width: 100%;
        padding: 0 var(--spacing-md);
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
        margin-right: var(--spacing-lg);
        text-align: right;
        padding-right: var(--spacing-md);
        direction: rtl;
    }

    .satisfaction-section,
    .runtime-section,
    .client-section {
        width: 31%;
        display: inline-block;
        padding: var(--spacing-xs);
        font-size: var(--font-size-xs);
        margin: 0 var(--spacing-xs);
        text-align: right;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .logo .brand-name {
        font-size: var(--font-size-2xl);
        color: var(--white);
    }
    
    .dots {
        font-size: var(--font-size-xl);
    }

    .content-section {
        padding: var(--spacing-md);
    }
}

/* Extra Small Devices (576px and up) */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        max-width: 540px;
        padding: 0 var(--spacing-lg);
    }

    .hero-title {
        font-size: var(--font-size-4xl);
        margin-right: var(--spacing-xl);
    }

    .satisfaction-section,
    .runtime-section,
    .client-section {
        width: 30%;
        display: inline-block;
        padding: var(--spacing-sm);
        font-size: var(--font-size-base);
    }
    
    .logo .brand-name {
        font-size: var(--font-size-2xl);
    }
    
    .dots {
        font-size: var(--font-size-xl);
    }
    
    .hero {
        min-height: 80vh;
        padding: var(--spacing-3xl) 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .btn-lg {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    .nav-brand .brand-name {
        font-size: var(--font-size-xl);
    }
    
    .features {
        padding: var(--spacing-2xl) 0;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .feature-card {
        padding: var(--spacing-xl);
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
    
    .services {
        padding: var(--spacing-2xl) 0;
    }
    
    .tab-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .tab-btn {
        width: 100%;
        text-align: center;
    }
    
    .tab-content {
        padding: var(--spacing-xl);
    }
    
    /* Dashboard Mobile Optimization for Small Tablets */
    .hero-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
        align-items: center;
    }

    .hero-text {
        order: 2;
        max-width: 100%;
    }

    .hero-visual {
        order: 1;
        width: 100%;
        max-width: 400px;
        margin: 0 auto var(--spacing-lg);
    }

    .modern-dashboard {
        max-width: 380px;
        margin: 0 auto;
    }

    .dashboard-main {
        background: rgba(255, 255, 255, 0.12);
        backdrop-filter: blur(12px);
        border: 1px solid rgba(255, 255, 255, 0.25);
        border-radius: 14px;
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-lg);
    }

    .metric-item {
        text-align: center;
        padding: var(--spacing-sm);
        background: rgba(255, 255, 255, 0.08);
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.15);
    }

    .metric-icon {
        width: 28px;
        height: 28px;
        margin: 0 auto var(--spacing-xs);
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 7px;
        font-size: 14px;
        color: var(--white);
    }

    .metric-data p {
        font-size: 11px;
        color: rgba(255, 255, 255, 0.8);
        margin: 4px 0 0;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
        margin-top: var(--spacing-lg);
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.08);
        border-radius: 10px;
        padding: var(--spacing-sm);
        text-align: center;
    }

    .hero-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
        max-width: 100%;
    }

    .floating-card {
        padding: var(--spacing-lg);
        font-size: var(--font-size-sm);
        min-height: 120px;
    }

    .floating-card .icon {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }

    .floating-card h4 {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
    }

    .floating-card p {
        font-size: var(--font-size-sm);
    }
}

/* Mobile Devices (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    /* Header Adjustments */
    .navbar {
        padding: var(--spacing-lg) 0;
    }

    .nav-brand {
        gap: var(--spacing-sm);
    }

    .logo {
        height: 32px;
    }

    .brand-name {
        font-size: var(--font-size-lg);
    }

    .nav-actions {
        gap: var(--spacing-sm);
    }

    .nav-actions .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    /* Language Switcher */
    .language-switcher {
        top: var(--spacing-sm);
        left: var(--spacing-sm);
    }

    .lang-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    /* Hero Section */
    .hero {
        min-height: 90vh;
        padding: 80px 0 40px;
    }

    .hero-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
        align-items: center;
    }

    .hero-text {
        order: 2;
        max-width: 100%;
    }

    .hero-visual {
        order: 1;
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
    }

    .hero-title {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-sm);
        line-height: 1.3;
    }

    .hero-subtitle {
        font-size: var(--font-size-xs);
        margin-bottom: var(--spacing-md);
        line-height: 1.4;
    }

    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
        width: 100%;
        max-width: 280px;
    }

    .btn-lg {
        width: 100%;
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    /* تحسين hero-badge للموبايل */
    .hero-badge {
        text-align: right !important;
        justify-content: flex-end !important;
        margin-right: var(--spacing-md) !important;
        margin-left: auto !important;
        direction: rtl !important;
        width: fit-content !important;
        margin-bottom: var(--spacing-md) !important;
    }

    /* تحسين hero-stats للموبايل - أفقي وأصغر */
    .hero-stats {
        display: flex !important;
        flex-direction: row !important;
        justify-content: center !important;
        gap: var(--spacing-xs) !important;
        margin: var(--spacing-md) auto !important;
        max-width: 100% !important;
        flex-wrap: nowrap !important;
    }

    .hero-stats .stat-item {
        flex: 1 1 auto !important;
        min-width: 70px !important;
        max-width: 90px !important;
        padding: var(--spacing-xs) var(--spacing-sm) !important;
        font-size: var(--font-size-xs) !important;
        text-align: center !important;
    }

    .hero-stats .stat-number {
        font-size: var(--font-size-lg) !important;
        margin-bottom: 2px !important;
    }

    .hero-stats .stat-plus {
        font-size: var(--font-size-sm) !important;
    }

    .hero-stats .stat-label {
        font-size: var(--font-size-xs) !important;
        line-height: 1.2 !important;
    }

    /* Mobile Dashboard Styles - Compact and Clean */
    .modern-dashboard {
        width: 100%;
        max-width: 320px;
        margin: 0 auto;
        position: relative;
    }

    .dashboard-main {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }

    .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .status-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 11px;
        color: rgba(255, 255, 255, 0.9);
    }

    .status-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #10b981;
        animation: pulse 2s infinite;
    }

    .dashboard-title h3 {
        font-size: 14px;
        color: var(--white);
        margin: 0;
        font-weight: 600;
    }

    /* Compact Metrics Grid for Mobile */
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 6px;
        margin-bottom: var(--spacing-md);
    }

    .metric-item {
        text-align: center;
        padding: 10px 6px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.25);
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .metric-item:hover {
        transform: translateY(-1px);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.1) 100%);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }

    .metric-icon {
        width: 20px;
        height: 20px;
        margin: 0 auto 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 6px;
        font-size: 10px;
        color: var(--white);
    }

    .metric-data {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .metric-value {
        font-size: 16px;
        font-weight: 700;
        color: var(--white);
        line-height: 1;
    }

    .metric-unit {
        font-size: 12px;
        color: var(--secondary-color);
        font-weight: 600;
    }

    .metric-data p {
        font-size: 9px;
        color: rgba(255, 255, 255, 0.8);
        margin: 2px 0 0;
        line-height: 1.2;
    }

    /* Progress Section - Compact */
    .progress-section {
        margin-top: var(--spacing-sm);
    }

    .progress-item {
        margin-bottom: 8px;
    }

    .progress-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        font-size: 11px;
        color: rgba(255, 255, 255, 0.9);
    }

    .progress-value {
        font-weight: 600;
        color: var(--secondary-color);
    }

    .progress-bar {
        height: 5px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 3px;
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4f46e5, #06b6d4);
        border-radius: 3px;
        transition: width 1s ease;
        box-shadow: 0 1px 3px rgba(79, 70, 229, 0.3);
    }

    /* Hide floating elements on mobile */
    .floating-elements {
        display: none;
    }

    /* Compact Stats Cards */
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
        margin-top: var(--spacing-sm);
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.08);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 8px;
        padding: 8px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-1px);
        background: rgba(255, 255, 255, 0.12);
    }

    .stat-icon {
        width: 24px;
        height: 24px;
        margin: 0 auto 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 6px;
        font-size: 12px;
        color: var(--white);
    }

    .stat-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .stat-number {
        font-size: 18px;
        font-weight: 700;
        color: var(--white);
        line-height: 1;
        margin-bottom: 2px;
    }

    .stat-label {
        font-size: 9px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.2;
    }

    /* Hero Stats Mobile Optimization - Compact Horizontal */
    .hero-stats {
        display: flex;
        flex-direction: row;
        gap: 3px;
        margin: 15px 0;
        max-width: 100%;
        justify-content: flex-start;
        flex-wrap: nowrap;
    }

    .hero-stats .stat-item {
        background: rgba(255, 255, 255, 0.08);
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 4px;
        padding: 4px 6px;
        text-align: center;
        min-height: 35px;
        min-width: 50px;
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .hero-stats .stat-item:hover {
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 0.18);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .hero-stats .stat-number {
        font-size: 9px;
        font-weight: 600;
        color: var(--white);
        line-height: 1;
        margin-bottom: 1px;
    }

    .hero-stats .stat-plus {
        font-size: 6px;
        color: var(--secondary-color);
        font-weight: 500;
        margin-left: 0.5px;
    }

    .hero-stats .stat-label {
        font-size: 5px;
        color: rgba(255, 255, 255, 0.8);
        margin-top: 1px;
        line-height: 1;
        text-transform: none;
        letter-spacing: 0;
        text-align: center;
    }

    /* Sections */
    .features,
    .services {
        padding: var(--spacing-xl) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-xl);
    }

    .section-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-sm);
    }

    .section-subtitle {
        font-size: var(--font-size-sm);
    }

    /* Features */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .feature-card {
        padding: var(--spacing-lg);
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }

    .feature-card h3 {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-sm);
    }

    .feature-card p {
        font-size: var(--font-size-sm);
    }

    /* Services */
    .tab-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .tab-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .tab-content {
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
    }

    /* Scroll Indicator */
    .scroll-indicator {
        bottom: 60px;
    }

    /* Classic Arrow Mobile Styles */
    .scroll-arrow {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    /* RTL Support for Mobile */
    [dir="rtl"] .hero-stats {
        justify-content: flex-end;
    }

    /* Old Arrow Mobile Styles */
    .scroll-arrow {
        height: 40px;
        width: 3px;
    }

    .scroll-arrow::after {
        width: 12px;
        height: 12px;
        left: -4.5px;
    }
}

/* Landscape Mobile Devices */
@media (max-width: 767px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }
    
    .hero-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        text-align: left;
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-buttons {
        flex-direction: row;
        gap: var(--spacing-md);
    }
    
    .btn-lg {
        width: auto;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .header,
    .language-switcher,
    .hero-buttons,
    .scroll-indicator {
        display: none !important;
    }
    
    .hero {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }
    
    .hero-background {
        display: none;
    }
    
    .hero-text {
        color: var(--gray-900) !important;
    }
    
    .section-title,
    .hero-title {
        color: var(--gray-900) !important;
    }
    
    .feature-card,
    .tab-content {
        box-shadow: none !important;
        border: 1px solid var(--gray-300) !important;
    }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .floating-card,
    .hero-particles,
    .scroll-arrow {
        animation: none !important;
    }
    
    .animate-fade-up,
    .animate-fade-left,
    .animate-fade-right,
    .animate-on-scroll {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
    }
}

/* Additional Mobile Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeInScale {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Mobile Dashboard Animations */
@media (max-width: 575px) {
    .dashboard-main {
        animation: slideInUp 0.6s ease-out;
    }

    .metric-item {
        animation: fadeInScale 0.5s ease-out;
        animation-fill-mode: both;
    }

    .metric-item:nth-child(1) { animation-delay: 0.1s; }
    .metric-item:nth-child(2) { animation-delay: 0.2s; }
    .metric-item:nth-child(3) { animation-delay: 0.3s; }

    .stat-card {
        animation: slideInUp 0.5s ease-out;
        animation-fill-mode: both;
    }

    .stat-card:nth-child(1) { animation-delay: 0.4s; }
    .stat-card:nth-child(2) { animation-delay: 0.5s; }

    .progress-fill {
        animation: progressFill 1.5s ease-out 0.8s both;
    }
}

@keyframes progressFill {
    from { width: 0; }
    to { width: var(--progress-width, 0%); }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1f2937;
        --gray-50: #111827;
        --gray-100: #1f2937;
        --gray-200: #374151;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }

    .header {
        background: rgba(31, 41, 55, 0.95);
        border-bottom-color: var(--gray-700);
    }

    .dropdown-menu {
        background: var(--gray-800);
        border: 1px solid var(--gray-700);
    }

    .lang-btn {
        background: var(--gray-800);
        border-color: var(--gray-700);
        color: var(--gray-200);
    }
}
